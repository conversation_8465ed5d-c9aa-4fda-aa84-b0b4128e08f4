// MCP Server Configuration
// Handles loading and validating configuration for the MCP server

import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load environment variables from .env file
dotenv.config();

export interface CacheConfig {
  enabled: boolean;
  type: 'memory' | 'redis';
  ttl: number; // Time to live in seconds
  maxSize: number; // Max size in MB for memory cache
  redisUrl?: string; // Redis connection URL (if type is 'redis')
}

export interface MCPConfig {
  // Server configuration
  port: number;
  environment: 'development' | 'production' | 'test';
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  version: string;
  
  // Security configuration
  apiKeys: string[];
  jwtSecret?: string;
  corsOrigins?: string | string[];
  rateLimit: number;
  
  // Feature flags
  enableDashboard: boolean;
  enableMetrics: boolean;
  enableFileStorage: boolean;
  
  // Cache configuration
  cache: CacheConfig;
  
  // File storage configuration
  fileStorage: {
    type: 'local' | 's3';
    basePath: string; // Local path or S3 bucket
    s3Region?: string;
    s3AccessKey?: string;
    s3SecretKey?: string;
  };
  
  // Legacy format support
  legacyFormats: {
    enableDOC: boolean;
    enableWordPerfect: boolean;
    enableOtherLegacyFormats: boolean;
  };
  
  // Batch processing
  batchProcessing: {
    maxConcurrentJobs: number;
    maxFilesPerBatch: number;
    maxBatchSizeMB: number;
  };
}

// Default configuration
const defaultConfig: MCPConfig = {
  port: 3030,
  environment: 'development',
  logLevel: 'info',
  version: '1.0.0',
  
  apiKeys: [],
  rateLimit: 100,
  
  enableDashboard: true,
  enableMetrics: true,
  enableFileStorage: true,
  
  cache: {
    enabled: true,
    type: 'memory',
    ttl: 3600, // 1 hour
    maxSize: 1024, // 1GB
  },
  
  fileStorage: {
    type: 'local',
    basePath: path.join(process.cwd(), 'storage'),
  },
  
  legacyFormats: {
    enableDOC: false,
    enableWordPerfect: false,
    enableOtherLegacyFormats: false,
  },
  
  batchProcessing: {
    maxConcurrentJobs: 5,
    maxFilesPerBatch: 100,
    maxBatchSizeMB: 1024, // 1GB
  },
};

// Load configuration from environment variables
export function loadConfig(): MCPConfig {
  const config = { ...defaultConfig };
  
  // Server configuration
  config.port = parseInt(process.env.MCP_PORT || process.env.PORT || String(config.port), 10);
  config.environment = (process.env.NODE_ENV || config.environment) as any;
  config.logLevel = (process.env.LOG_LEVEL || config.logLevel) as any;
  config.version = process.env.VERSION || config.version;
  
  // Security configuration
  config.apiKeys = process.env.API_KEYS ? process.env.API_KEYS.split(',') : config.apiKeys;
  config.jwtSecret = process.env.JWT_SECRET || config.jwtSecret;
  config.corsOrigins = process.env.CORS_ORIGINS || config.corsOrigins;
  config.rateLimit = parseInt(process.env.RATE_LIMIT || String(config.rateLimit), 10);
  
  // Feature flags
  config.enableDashboard = process.env.ENABLE_DASHBOARD === 'true' || config.enableDashboard;
  config.enableMetrics = process.env.ENABLE_METRICS === 'true' || config.enableMetrics;
  config.enableFileStorage = process.env.ENABLE_FILE_STORAGE === 'true' || config.enableFileStorage;
  
  // Cache configuration
  config.cache.enabled = process.env.CACHE_ENABLED === 'true' || config.cache.enabled;
  config.cache.type = (process.env.CACHE_TYPE || config.cache.type) as any;
  config.cache.ttl = parseInt(process.env.CACHE_TTL || String(config.cache.ttl), 10);
  config.cache.maxSize = parseInt(process.env.CACHE_MAX_SIZE || String(config.cache.maxSize), 10);
  config.cache.redisUrl = process.env.REDIS_URL || config.cache.redisUrl;
  
  // File storage configuration
  config.fileStorage.type = (process.env.FILE_STORAGE_TYPE || config.fileStorage.type) as any;
  config.fileStorage.basePath = process.env.FILE_STORAGE_PATH || config.fileStorage.basePath;
  config.fileStorage.s3Region = process.env.S3_REGION || config.fileStorage.s3Region;
  config.fileStorage.s3AccessKey = process.env.S3_ACCESS_KEY || config.fileStorage.s3AccessKey;
  config.fileStorage.s3SecretKey = process.env.S3_SECRET_KEY || config.fileStorage.s3SecretKey;
  
  // Legacy format support
  config.legacyFormats.enableDOC = process.env.ENABLE_DOC === 'true' || config.legacyFormats.enableDOC;
  config.legacyFormats.enableWordPerfect = process.env.ENABLE_WORDPERFECT === 'true' || config.legacyFormats.enableWordPerfect;
  config.legacyFormats.enableOtherLegacyFormats = process.env.ENABLE_OTHER_LEGACY_FORMATS === 'true' || config.legacyFormats.enableOtherLegacyFormats;
  
  // Batch processing
  config.batchProcessing.maxConcurrentJobs = parseInt(process.env.MAX_CONCURRENT_JOBS || String(config.batchProcessing.maxConcurrentJobs), 10);
  config.batchProcessing.maxFilesPerBatch = parseInt(process.env.MAX_FILES_PER_BATCH || String(config.batchProcessing.maxFilesPerBatch), 10);
  config.batchProcessing.maxBatchSizeMB = parseInt(process.env.MAX_BATCH_SIZE_MB || String(config.batchProcessing.maxBatchSizeMB), 10);
  
  return config;
}

// Validate configuration
export function validateConfig(config: MCPConfig): string[] {
  const errors: string[] = [];
  
  // Validate required fields
  if (!config.port) {
    errors.push('Port is required');
  }
  
  // Validate security configuration
  if (config.environment === 'production') {
    if (!config.apiKeys || config.apiKeys.length === 0) {
      errors.push('API keys are required in production');
    }
    
    if (!config.jwtSecret) {
      errors.push('JWT secret is required in production');
    }
  }
  
  // Validate cache configuration
  if (config.cache.enabled && config.cache.type === 'redis' && !config.cache.redisUrl) {
    errors.push('Redis URL is required when Redis cache is enabled');
  }
  
  // Validate file storage configuration
  if (config.enableFileStorage) {
    if (config.fileStorage.type === 'local') {
      // Ensure storage directory exists
      try {
        if (!fs.existsSync(config.fileStorage.basePath)) {
          fs.mkdirSync(config.fileStorage.basePath, { recursive: true });
        }
      } catch (error) {
        errors.push(`Failed to create storage directory: ${error}`);
      }
    } else if (config.fileStorage.type === 's3') {
      if (!config.fileStorage.s3Region || !config.fileStorage.s3AccessKey || !config.fileStorage.s3SecretKey) {
        errors.push('S3 configuration is incomplete');
      }
    }
  }
  
  return errors;
}

// Get configuration
export function getConfig(): MCPConfig {
  const config = loadConfig();
  const errors = validateConfig(config);
  
  if (errors.length > 0) {
    console.error('Configuration errors:');
    errors.forEach(error => console.error(`- ${error}`));
    
    if (config.environment === 'production') {
      throw new Error('Invalid configuration in production environment');
    }
  }
  
  return config;
}