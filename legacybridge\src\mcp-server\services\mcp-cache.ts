// MCP Server Cache Service
// Provides caching for conversion results to improve performance

import Redis from 'ioredis';
import { LRUCache } from 'lru-cache';
import crypto from 'crypto';
import { CacheConfig } from '../utils/mcp-config';

export interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  keys: number;
  hitRate: number;
}

export class MCPCache {
  private config: CacheConfig;
  private memoryCache: LRUCache<string, any> | null = null;
  private redisClient: Redis | null = null;
  private stats: {
    hits: number;
    misses: number;
  } = {
    hits: 0,
    misses: 0,
  };

  constructor(config: CacheConfig) {
    this.config = config;
    
    if (!config.enabled) {
      return;
    }
    
    if (config.type === 'memory') {
      this.initializeMemoryCache();
    } else if (config.type === 'redis') {
      this.initializeRedisCache();
    }
  }

  private initializeMemoryCache(): void {
    this.memoryCache = new LRUCache({
      max: 1000, // Maximum number of items
      maxSize: this.config.maxSize * 1024 * 1024, // Convert MB to bytes
      ttl: this.config.ttl * 1000, // Convert seconds to milliseconds
      sizeCalculation: this.getSizeInBytes,
      updateAgeOnGet: true,
    });
  }

  private initializeRedisCache(): void {
    if (!this.config.redisUrl) {
      console.warn('Redis URL not provided, falling back to memory cache');
      this.initializeMemoryCache();
      return;
    }
    
    this.redisClient = new Redis(this.config.redisUrl, {
      maxRetriesPerRequest: 3,
      enableOfflineQueue: false,
    });
    
    this.redisClient.on('error', (error) => {
      console.error('Redis connection error:', error);
    });
  }

  // Calculate size of cached item in bytes
  private getSizeInBytes(value: any): number {
    if (typeof value === 'string') {
      return Buffer.byteLength(value, 'utf8');
    }
    
    return Buffer.byteLength(JSON.stringify(value), 'utf8');
  }

  // Generate cache key
  private generateKey(type: string, content: string, options?: any): string {
    const hash = crypto.createHash('md5');
    hash.update(content);
    
    if (options) {
      hash.update(JSON.stringify(options));
    }
    
    return `${type}:${hash.digest('hex')}`;
  }

  // Set value in cache
  public async set(type: string, content: string, result: any, options?: any): Promise<void> {
    if (!this.config.enabled) {
      return;
    }
    
    const key = this.generateKey(type, content, options);
    const value = JSON.stringify(result);
    
    if (this.memoryCache) {
      this.memoryCache.set(key, value);
    } else if (this.redisClient) {
      await this.redisClient.setex(key, this.config.ttl, value);
    }
  }

  // Get value from cache
  public async get(type: string, content: string, options?: any): Promise<any | null> {
    if (!this.config.enabled) {
      this.stats.misses++;
      return null;
    }
    
    const key = this.generateKey(type, content, options);
    
    if (this.memoryCache) {
      const value = this.memoryCache.get(key);
      
      if (value) {
        this.stats.hits++;
        return JSON.parse(value as string);
      }
    } else if (this.redisClient) {
      const value = await this.redisClient.get(key);
      
      if (value) {
        this.stats.hits++;
        return JSON.parse(value);
      }
    }
    
    this.stats.misses++;
    return null;
  }

  // Delete value from cache
  public async delete(type: string, content: string, options?: any): Promise<void> {
    if (!this.config.enabled) {
      return;
    }
    
    const key = this.generateKey(type, content, options);
    
    if (this.memoryCache) {
      this.memoryCache.delete(key);
    } else if (this.redisClient) {
      await this.redisClient.del(key);
    }
  }

  // Clear entire cache
  public async clear(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }
    
    if (this.memoryCache) {
      this.memoryCache.clear();
    } else if (this.redisClient) {
      await this.redisClient.flushdb();
    }
  }

  // Get cache statistics
  public getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    const hitRate = total > 0 ? this.stats.hits / total : 0;
    
    if (this.memoryCache) {
      return {
        hits: this.stats.hits,
        misses: this.stats.misses,
        size: this.memoryCache.calculatedSize || 0,
        keys: this.memoryCache.size,
        hitRate,
      };
    } else if (this.redisClient) {
      // Redis stats are more limited without additional queries
      return {
        hits: this.stats.hits,
        misses: this.stats.misses,
        size: 0, // Not available for Redis without additional queries
        keys: 0, // Not available for Redis without additional queries
        hitRate,
      };
    }
    
    return {
      hits: 0,
      misses: 0,
      size: 0,
      keys: 0,
      hitRate: 0,
    };
  }

  // Close connections
  public close(): void {
    if (this.redisClient) {
      this.redisClient.disconnect();
    }
  }
}