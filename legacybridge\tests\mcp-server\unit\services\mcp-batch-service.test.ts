// Unit tests for MCPBatchService
import { MCPBatchService } from '../../../../src/mcp-server/services/mcp-batch-service';
import { MCPConversionService } from '../../../../src/mcp-server/services/mcp-conversion-service';
import { mockMCPConfig } from '../../mocks/config.mock';
import { MockMCPLogger } from '../../mocks/logger.mock';
import axios from 'axios';

// Mock axios
jest.mock('axios', () => ({
  post: jest.fn()
}));

// Mock MCPConversionService
jest.mock('../../../../src/mcp-server/services/mcp-conversion-service', () => {
  return {
    MCPConversionService: jest.fn().mockImplementation(() => ({
      convertRtfToMarkdown: jest.fn().mockResolvedValue({
        content: '# Converted Markdown',
        metadata: {
          convertedAt: new Date().toISOString(),
          originalFormat: 'RTF'
        }
      }),
      convertMarkdownToRtf: jest.fn().mockResolvedValue({
        content: '{\\rtf1\\ansi Converted RTF}',
        metadata: {
          convertedAt: new Date().toISOString(),
          originalFormat: 'Markdown'
        }
      })
    }))
  };
});

describe('MCPBatchService', () => {
  let batchService: MCPBatchService;
  let mockLogger: MockMCPLogger;
  let mockConversionService: any;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockLogger = new MockMCPLogger();
    mockConversionService = new MCPConversionService();
    
    batchService = new MCPBatchService(mockMCPConfig, mockConversionService, mockLogger as any);
  });
  
  describe('submitBatch', () => {
    test('should submit a batch job', async () => {
      const files = [
        {
          content: '{\\rtf1\\ansi Test RTF Content}',
          fileName: 'test1.rtf',
          conversionType: 'rtf_to_md'
        },
        {
          content: '# Test Markdown Content',
          fileName: 'test2.md',
          conversionType: 'md_to_rtf'
        }
      ];
      
      const batchOptions = {
        priority: 'normal',
        callbackUrl: 'https://example.com/callback'
      };
      
      const result = await batchService.submitBatch(files, batchOptions);
      
      expect(result).toHaveProperty('batchId');
      expect(result).toHaveProperty('status', 'submitted');
      expect(result).toHaveProperty('totalFiles', 2);
      expect(result).toHaveProperty('priority', 'normal');
      expect(result).toHaveProperty('submittedAt');
      
      // Verify batch is stored
      const storedBatch = (batchService as any).batches.get(result.batchId);
      expect(storedBatch).toBeDefined();
      expect(storedBatch).toHaveProperty('files', files);
      expect(storedBatch).toHaveProperty('options', batchOptions);
      
      // Verify processing started
      expect((batchService as any).processBatch).toHaveBeenCalledWith(result.batchId);
    });
    
    test('should validate batch input', async () => {
      // Empty files array
      await expect(batchService.submitBatch([], {})).rejects.toThrow(
        'Batch must contain at least one file'
      );
      
      // Invalid conversion type
      await expect(batchService.submitBatch([
        {
          content: 'Test content',
          fileName: 'test.txt',
          conversionType: 'invalid_conversion'
        }
      ], {})).rejects.toThrow(
        'Invalid conversion type: invalid_conversion'
      );
      
      // Missing required fields
      await expect(batchService.submitBatch([
        {
          content: 'Test content',
          fileName: 'test.txt'
        } as any
      ], {})).rejects.toThrow(
        'Missing required field: conversionType'
      );
      
      await expect(batchService.submitBatch([
        {
          content: 'Test content',
          conversionType: 'rtf_to_md'
        } as any
      ], {})).rejects.toThrow(
        'Missing required field: fileName'
      );
      
      await expect(batchService.submitBatch([
        {
          fileName: 'test.rtf',
          conversionType: 'rtf_to_md'
        } as any
      ], {})).rejects.toThrow(
        'Missing required field: content'
      );
    });
  });
  
  describe('getBatchStatus', () => {
    test('should get batch status', async () => {
      // Submit a batch first
      const files = [
        {
          content: '{\\rtf1\\ansi Test RTF Content}',
          fileName: 'test.rtf',
          conversionType: 'rtf_to_md'
        }
      ];
      
      const result = await batchService.submitBatch(files, {});
      const batchId = result.batchId;
      
      // Get status
      const status = await batchService.getBatchStatus(batchId);
      
      expect(status).toHaveProperty('batchId', batchId);
      expect(status).toHaveProperty('status');
      expect(status).toHaveProperty('totalFiles', 1);
      expect(status).toHaveProperty('completedFiles');
      expect(status).toHaveProperty('progress');
      expect(status).toHaveProperty('submittedAt');
    });
    
    test('should throw error for non-existent batch', async () => {
      await expect(batchService.getBatchStatus('non-existent-batch')).rejects.toThrow(
        'Batch not found: non-existent-batch'
      );
    });
  });
  
  describe('cancelBatch', () => {
    test('should cancel a batch job', async () => {
      // Submit a batch first
      const files = [
        {
          content: '{\\rtf1\\ansi Test RTF Content}',
          fileName: 'test.rtf',
          conversionType: 'rtf_to_md'
        }
      ];
      
      const result = await batchService.submitBatch(files, {});
      const batchId = result.batchId;
      
      // Cancel batch
      await batchService.cancelBatch(batchId);
      
      // Get status
      const status = await batchService.getBatchStatus(batchId);
      
      expect(status).toHaveProperty('status', 'cancelled');
    });
    
    test('should throw error for non-existent batch', async () => {
      await expect(batchService.cancelBatch('non-existent-batch')).rejects.toThrow(
        'Batch not found: non-existent-batch'
      );
    });
  });
  
  describe('processBatch', () => {
    test('should process batch files', async () => {
      // Submit a batch first
      const files = [
        {
          content: '{\\rtf1\\ansi Test RTF Content}',
          fileName: 'test1.rtf',
          conversionType: 'rtf_to_md'
        },
        {
          content: '# Test Markdown Content',
          fileName: 'test2.md',
          conversionType: 'md_to_rtf'
        }
      ];
      
      const result = await batchService.submitBatch(files, {});
      const batchId = result.batchId;
      
      // Mock processBatch to resolve immediately
      (batchService as any).processBatch = jest.fn().mockResolvedValue(undefined);
      
      // Call processBatch directly
      await (batchService as any).processBatch(batchId);
      
      // Verify conversion service was called
      expect(mockConversionService.convertRtfToMarkdown).toHaveBeenCalled();
      expect(mockConversionService.convertMarkdownToRtf).toHaveBeenCalled();
    });
    
    test('should handle conversion errors', async () => {
      // Mock conversion error
      mockConversionService.convertRtfToMarkdown.mockRejectedValue(new Error('Conversion failed'));
      
      // Submit a batch
      const files = [
        {
          content: '{\\rtf1\\ansi Test RTF Content}',
          fileName: 'test.rtf',
          conversionType: 'rtf_to_md'
        }
      ];
      
      const result = await batchService.submitBatch(files, {});
      const batchId = result.batchId;
      
      // Mock processBatch to resolve immediately
      (batchService as any).processBatch = jest.fn().mockResolvedValue(undefined);
      
      // Call processBatch directly
      await (batchService as any).processBatch(batchId);
      
      // Verify error was logged
      expect(mockLogger.error).toHaveBeenCalled();
      
      // Get status
      const status = await batchService.getBatchStatus(batchId);
      
      // Batch should be marked as failed
      expect(status).toHaveProperty('status', 'failed');
      expect(status).toHaveProperty('error');
    });
    
    test('should call callback URL when batch is complete', async () => {
      // Submit a batch with callback URL
      const files = [
        {
          content: '{\\rtf1\\ansi Test RTF Content}',
          fileName: 'test.rtf',
          conversionType: 'rtf_to_md'
        }
      ];
      
      const batchOptions = {
        callbackUrl: 'https://example.com/callback'
      };
      
      const result = await batchService.submitBatch(files, batchOptions);
      const batchId = result.batchId;
      
      // Mock processBatch to resolve immediately
      (batchService as any).processBatch = jest.fn().mockResolvedValue(undefined);
      
      // Call processBatch directly
      await (batchService as any).processBatch(batchId);
      
      // Verify callback was called
      expect(axios.post).toHaveBeenCalledWith(
        'https://example.com/callback',
        expect.objectContaining({
          batchId,
          status: 'completed'
        })
      );
    });
    
    test('should handle callback errors', async () => {
      // Mock callback error
      (axios.post as jest.Mock).mockRejectedValue(new Error('Callback failed'));
      
      // Submit a batch with callback URL
      const files = [
        {
          content: '{\\rtf1\\ansi Test RTF Content}',
          fileName: 'test.rtf',
          conversionType: 'rtf_to_md'
        }
      ];
      
      const batchOptions = {
        callbackUrl: 'https://example.com/callback'
      };
      
      const result = await batchService.submitBatch(files, batchOptions);
      const batchId = result.batchId;
      
      // Mock processBatch to resolve immediately
      (batchService as any).processBatch = jest.fn().mockResolvedValue(undefined);
      
      // Call processBatch directly
      await (batchService as any).processBatch(batchId);
      
      // Verify error was logged
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send callback'),
        expect.any(Error)
      );
      
      // Batch should still be marked as completed
      const status = await batchService.getBatchStatus(batchId);
      expect(status).toHaveProperty('status', 'completed');
    });
  });
});