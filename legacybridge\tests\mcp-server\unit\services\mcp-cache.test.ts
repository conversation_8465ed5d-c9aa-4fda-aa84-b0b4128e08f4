// Unit tests for MCPCache
import { MC<PERSON>ache } from '../../../../src/mcp-server/services/mcp-cache';
import { mockMCPConfig } from '../../mocks/config.mock';

// Mock LRU Cache
jest.mock('lru-cache', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
    clear: jest.fn()
  }));
});

// Mock Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    flushall: jest.fn(),
    quit: jest.fn(),
    on: jest.fn()
  }));
});

describe('MCPCache', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('Memory Cache', () => {
    test('should create memory cache when type is memory', () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'memory'
      };
      
      const cache = new MCPCache(config);
      
      expect(cache).toBeDefined();
      expect(cache.type).toBe('memory');
    });
    
    test('should get value from memory cache', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'memory'
      };
      
      const cache = new MCPCache(config);
      const mockValue = { data: 'test-data' };
      
      // Mock cache hit
      cache.memoryCache.get.mockReturnValue(mockValue);
      
      const result = await cache.get('test-key');
      
      expect(cache.memoryCache.get).toHaveBeenCalledWith('test-key');
      expect(result).toEqual(mockValue);
    });
    
    test('should set value in memory cache', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'memory'
      };
      
      const cache = new MCPCache(config);
      const mockValue = { data: 'test-data' };
      
      await cache.set('test-key', mockValue);
      
      expect(cache.memoryCache.set).toHaveBeenCalledWith('test-key', mockValue);
    });
    
    test('should delete value from memory cache', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'memory'
      };
      
      const cache = new MCPCache(config);
      
      await cache.delete('test-key');
      
      expect(cache.memoryCache.delete).toHaveBeenCalledWith('test-key');
    });
    
    test('should clear memory cache', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'memory'
      };
      
      const cache = new MCPCache(config);
      
      await cache.clear();
      
      expect(cache.memoryCache.clear).toHaveBeenCalled();
    });
  });
  
  describe('Redis Cache', () => {
    test('should create redis cache when type is redis', () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'redis',
        redis: {
          url: 'redis://localhost:6379'
        }
      };
      
      const cache = new MCPCache(config);
      
      expect(cache).toBeDefined();
      expect(cache.type).toBe('redis');
    });
    
    test('should get value from redis cache', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'redis',
        redis: {
          url: 'redis://localhost:6379'
        }
      };
      
      const cache = new MCPCache(config);
      const mockValue = JSON.stringify({ data: 'test-data' });
      
      // Mock Redis get
      cache.redisClient.get.mockResolvedValue(mockValue);
      
      const result = await cache.get('test-key');
      
      expect(cache.redisClient.get).toHaveBeenCalledWith('mcp:test-key');
      expect(result).toEqual({ data: 'test-data' });
    });
    
    test('should handle null value from redis cache', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'redis',
        redis: {
          url: 'redis://localhost:6379'
        }
      };
      
      const cache = new MCPCache(config);
      
      // Mock Redis get returning null
      cache.redisClient.get.mockResolvedValue(null);
      
      const result = await cache.get('test-key');
      
      expect(cache.redisClient.get).toHaveBeenCalledWith('mcp:test-key');
      expect(result).toBeNull();
    });
    
    test('should set value in redis cache', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'redis',
        redis: {
          url: 'redis://localhost:6379'
        },
        ttl: 3600
      };
      
      const cache = new MCPCache(config);
      const mockValue = { data: 'test-data' };
      
      await cache.set('test-key', mockValue);
      
      expect(cache.redisClient.set).toHaveBeenCalledWith(
        'mcp:test-key',
        JSON.stringify(mockValue),
        'EX',
        3600
      );
    });
    
    test('should delete value from redis cache', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'redis',
        redis: {
          url: 'redis://localhost:6379'
        }
      };
      
      const cache = new MCPCache(config);
      
      await cache.delete('test-key');
      
      expect(cache.redisClient.del).toHaveBeenCalledWith('mcp:test-key');
    });
    
    test('should clear redis cache', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'redis',
        redis: {
          url: 'redis://localhost:6379'
        }
      };
      
      const cache = new MCPCache(config);
      
      await cache.clear();
      
      expect(cache.redisClient.flushall).toHaveBeenCalled();
    });
    
    test('should close redis connection', async () => {
      const config = {
        ...mockMCPConfig.cache,
        type: 'redis',
        redis: {
          url: 'redis://localhost:6379'
        }
      };
      
      const cache = new MCPCache(config);
      
      await cache.close();
      
      expect(cache.redisClient.quit).toHaveBeenCalled();
    });
  });
  
  describe('Disabled Cache', () => {
    test('should create disabled cache when enabled is false', () => {
      const config = {
        ...mockMCPConfig.cache,
        enabled: false
      };
      
      const cache = new MCPCache(config);
      
      expect(cache).toBeDefined();
      expect(cache.type).toBe('disabled');
    });
    
    test('should return null for get when cache is disabled', async () => {
      const config = {
        ...mockMCPConfig.cache,
        enabled: false
      };
      
      const cache = new MCPCache(config);
      
      const result = await cache.get('test-key');
      
      expect(result).toBeNull();
    });
    
    test('should do nothing for set when cache is disabled', async () => {
      const config = {
        ...mockMCPConfig.cache,
        enabled: false
      };
      
      const cache = new MCPCache(config);
      
      await cache.set('test-key', { data: 'test-data' });
      
      // No error should be thrown
      expect(true).toBe(true);
    });
    
    test('should do nothing for delete when cache is disabled', async () => {
      const config = {
        ...mockMCPConfig.cache,
        enabled: false
      };
      
      const cache = new MCPCache(config);
      
      await cache.delete('test-key');
      
      // No error should be thrown
      expect(true).toBe(true);
    });
    
    test('should do nothing for clear when cache is disabled', async () => {
      const config = {
        ...mockMCPConfig.cache,
        enabled: false
      };
      
      const cache = new MCPCache(config);
      
      await cache.clear();
      
      // No error should be thrown
      expect(true).toBe(true);
    });
    
    test('should do nothing for close when cache is disabled', async () => {
      const config = {
        ...mockMCPConfig.cache,
        enabled: false
      };
      
      const cache = new MCPCache(config);
      
      await cache.close();
      
      // No error should be thrown
      expect(true).toBe(true);
    });
  });
});