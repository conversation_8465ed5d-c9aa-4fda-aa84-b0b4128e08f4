// MCP Batch Service
// Handles batch document conversions with progress tracking

import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { MCPConfig } from '../utils/mcp-config';
import { MCPLogger } from '../utils/mcp-logger';
import { MCPConversionService, ConversionOptions } from './mcp-conversion-service';
import { ValidationError, NotFoundError } from '../middleware/mcp-error-handler';

export interface BatchFile {
  content: string;
  fileName: string;
  conversionType: 'rtf_to_md' | 'md_to_rtf' | 'doc_to_md' | 'wp_to_md';
  options?: ConversionOptions;
}

export interface BatchOptions {
  priority?: 'low' | 'normal' | 'high';
  callbackUrl?: string;
}

export interface BatchStatus {
  batchId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  totalFiles: number;
  processedFiles: number;
  successfulFiles: number;
  failedFiles: number;
  startTime: string;
  endTime?: string;
  estimatedTimeRemaining?: number;
  results?: Array<{
    fileName: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    error?: string;
    outputFileName?: string;
  }>;
}

export class MCPBatchService {
  private batches: Map<string, BatchStatus> = new Map();
  private batchWorkers: Map<string, NodeJS.Timeout> = new Map();
  
  constructor(
    private config: MCPConfig,
    private conversionService: MCPConversionService,
    private logger: MCPLogger
  ) {}

  // Submit a batch conversion job
  async submitBatch(files: BatchFile[], options?: BatchOptions): Promise<{ batchId: string }> {
    this.logger.info('Submitting batch conversion', { 
      fileCount: files.length,
      priority: options?.priority || 'normal'
    });
    
    // Validate batch
    if (!files || files.length === 0) {
      throw new ValidationError('No files provided for batch conversion');
    }
    
    if (files.length > this.config.batchProcessing.maxFilesPerBatch) {
      throw new ValidationError(`Batch exceeds maximum file count (${this.config.batchProcessing.maxFilesPerBatch})`);
    }
    
    // Create batch ID
    const batchId = uuidv4();
    
    // Initialize batch status
    const batchStatus: BatchStatus = {
      batchId,
      status: 'pending',
      progress: 0,
      totalFiles: files.length,
      processedFiles: 0,
      successfulFiles: 0,
      failedFiles: 0,
      startTime: new Date().toISOString(),
      results: files.map(file => ({
        fileName: file.fileName,
        status: 'pending'
      }))
    };
    
    this.batches.set(batchId, batchStatus);
    
    // Start batch processing
    this.processBatch(batchId, files, options);
    
    return { batchId };
  }

  // Get batch status
  async getBatchStatus(batchId: string): Promise<BatchStatus> {
    const batch = this.batches.get(batchId);
    
    if (!batch) {
      throw new NotFoundError(`Batch not found: ${batchId}`);
    }
    
    return batch;
  }

  // Cancel batch
  async cancelBatch(batchId: string): Promise<void> {
    const batch = this.batches.get(batchId);
    
    if (!batch) {
      throw new NotFoundError(`Batch not found: ${batchId}`);
    }
    
    // Can only cancel pending or processing batches
    if (batch.status !== 'pending' && batch.status !== 'processing') {
      throw new ValidationError(`Cannot cancel batch with status: ${batch.status}`);
    }
    
    // Clear the batch worker
    const worker = this.batchWorkers.get(batchId);
    if (worker) {
      clearTimeout(worker);
      this.batchWorkers.delete(batchId);
    }
    
    // Update batch status
    batch.status = 'cancelled';
    batch.endTime = new Date().toISOString();
    
    this.logger.info(`Batch ${batchId} cancelled`);
  }

  // Process batch
  private processBatch(batchId: string, files: BatchFile[], options?: BatchOptions): void {
    const batch = this.batches.get(batchId);
    
    if (!batch) {
      this.logger.error(`Batch ${batchId} not found`);
      return;
    }
    
    // Update batch status
    batch.status = 'processing';
    
    // Process files sequentially with delay based on priority
    const delay = this.getPriorityDelay(options?.priority);
    
    // Start processing
    this.processNextFile(batchId, files, 0, delay);
  }

  // Process next file in batch
  private processNextFile(batchId: string, files: BatchFile[], index: number, delay: number): void {
    const batch = this.batches.get(batchId);
    
    if (!batch || batch.status === 'cancelled') {
      return;
    }
    
    // All files processed
    if (index >= files.length) {
      this.completeBatch(batchId);
      return;
    }
    
    // Schedule next file with delay
    const worker = setTimeout(async () => {
      const file = files[index];
      const result = batch.results?.[index];
      
      if (!result) {
        this.logger.error(`Result not found for file at index ${index}`);
        this.processNextFile(batchId, files, index + 1, delay);
        return;
      }
      
      // Update file status
      result.status = 'processing';
      
      try {
        this.logger.info(`Processing file ${index + 1}/${files.length}: ${file.fileName}`);
        
        // Process file based on conversion type
        let conversionResult;
        
        switch (file.conversionType) {
          case 'rtf_to_md':
            conversionResult = await this.conversionService.convertRtfToMarkdown(file.content, file.options);
            break;
            
          case 'md_to_rtf':
            conversionResult = await this.conversionService.convertMarkdownToRtf(file.content, file.options);
            break;
            
          default:
            throw new ValidationError(`Unsupported conversion type: ${file.conversionType}`);
        }
        
        // Generate output file name
        const fileExt = path.extname(file.fileName);
        const baseName = path.basename(file.fileName, fileExt);
        const outputExt = file.conversionType === 'rtf_to_md' ? '.md' : '.rtf';
        const outputFileName = `${baseName}${outputExt}`;
        
        // Update file status
        result.status = 'completed';
        result.outputFileName = outputFileName;
        
        // Save output file if needed
        if (this.config.enableFileStorage) {
          const outputDir = path.join(process.cwd(), 'output', batchId);
          
          // Create output directory if it doesn't exist
          if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
          }
          
          const outputPath = path.join(outputDir, outputFileName);
          
          // Write output file
          await fs.promises.writeFile(outputPath, conversionResult.content, 'utf8');
        }
        
        // Update batch status
        batch.processedFiles++;
        batch.successfulFiles++;
        batch.progress = Math.floor((batch.processedFiles / batch.totalFiles) * 100);
        
      } catch (error) {
        this.logger.error(`Error processing file ${file.fileName}`, error);
        
        // Update file status
        result.status = 'failed';
        result.error = error instanceof Error ? error.message : String(error);
        
        // Update batch status
        batch.processedFiles++;
        batch.failedFiles++;
        batch.progress = Math.floor((batch.processedFiles / batch.totalFiles) * 100);
      }
      
      // Calculate estimated time remaining
      if (batch.processedFiles > 0) {
        const startTime = new Date(batch.startTime).getTime();
        const now = Date.now();
        const elapsed = now - startTime;
        const avgTimePerFile = elapsed / batch.processedFiles;
        const remainingFiles = batch.totalFiles - batch.processedFiles;
        const estimatedTimeRemaining = Math.floor((avgTimePerFile * remainingFiles) / 1000); // in seconds
        
        batch.estimatedTimeRemaining = estimatedTimeRemaining;
      }
      
      // Process next file
      this.processNextFile(batchId, files, index + 1, delay);
      
    }, delay);
    
    // Store worker reference for cancellation
    this.batchWorkers.set(batchId, worker);
  }

  // Complete batch
  private async completeBatch(batchId: string): Promise<void> {
    const batch = this.batches.get(batchId);
    
    if (!batch) {
      return;
    }
    
    // Update batch status
    batch.status = batch.failedFiles === 0 ? 'completed' : 'failed';
    batch.endTime = new Date().toISOString();
    batch.progress = 100;
    batch.estimatedTimeRemaining = 0;
    
    this.logger.info(`Batch ${batchId} completed`, {
      totalFiles: batch.totalFiles,
      successfulFiles: batch.successfulFiles,
      failedFiles: batch.failedFiles,
      duration: new Date(batch.endTime).getTime() - new Date(batch.startTime).getTime()
    });
    
    // Clean up
    this.batchWorkers.delete(batchId);
    
    // Send callback if provided
    const options = this.getBatchOptions(batchId);
    if (options?.callbackUrl) {
      try {
        await axios.post(options.callbackUrl, {
          batchId,
          status: batch.status,
          totalFiles: batch.totalFiles,
          successfulFiles: batch.successfulFiles,
          failedFiles: batch.failedFiles,
          completedAt: batch.endTime
        });
        
        this.logger.info(`Callback sent for batch ${batchId}`);
      } catch (error) {
        this.logger.error(`Failed to send callback for batch ${batchId}`, error);
      }
    }
    
    // Keep batch status for a limited time (e.g., 24 hours)
    setTimeout(() => {
      this.batches.delete(batchId);
      this.logger.info(`Batch ${batchId} status removed from memory`);
    }, 24 * 60 * 60 * 1000);
  }

  // Get priority delay
  private getPriorityDelay(priority?: string): number {
    switch (priority) {
      case 'high':
        return 0;
      case 'normal':
        return 100;
      case 'low':
        return 500;
      default:
        return 100;
    }
  }

  // Get batch options (for testing/mocking)
  private getBatchOptions(batchId: string): BatchOptions | undefined {
    // In a real implementation, this would retrieve the options from storage
    return undefined;
  }
}