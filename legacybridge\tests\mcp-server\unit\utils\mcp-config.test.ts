// Unit tests for MCPConfig
import { MCPConfig } from '../../../../src/mcp-server/utils/mcp-config';

describe('MCPConfig', () => {
  // Store original environment variables
  const originalEnv = process.env;
  
  beforeEach(() => {
    // Reset environment variables before each test
    jest.resetModules();
    process.env = { ...originalEnv };
  });
  
  afterAll(() => {
    // Restore original environment variables
    process.env = originalEnv;
  });
  
  test('should load default configuration', () => {
    // Clear environment variables that might affect the test
    delete process.env.NODE_ENV;
    delete process.env.MCP_PORT;
    delete process.env.LOG_LEVEL;
    delete process.env.CACHE_ENABLED;
    delete process.env.CACHE_TYPE;
    delete process.env.API_KEYS;
    delete process.env.JWT_SECRET;
    delete process.env.ENABLE_DOC;
    delete process.env.ENABLE_WORDPERFECT;
    
    const config = new MCPConfig();
    
    // Verify default values
    expect(config.environment).toBe('development');
    expect(config.port).toBe(3030);
    expect(config.logLevel).toBe('info');
    expect(config.cache.enabled).toBe(true);
    expect(config.cache.type).toBe('memory');
    expect(config.auth.apiKeys).toEqual([]);
    expect(config.legacyFormats.enableDOC).toBe(false);
    expect(config.legacyFormats.enableWordPerfect).toBe(false);
  });
  
  test('should load configuration from environment variables', () => {
    // Set environment variables
    process.env.NODE_ENV = 'production';
    process.env.MCP_PORT = '4000';
    process.env.LOG_LEVEL = 'warn';
    process.env.CACHE_ENABLED = 'true';
    process.env.CACHE_TYPE = 'redis';
    process.env.REDIS_URL = 'redis://localhost:6379';
    process.env.API_KEYS = 'key1,key2,key3';
    process.env.JWT_SECRET = 'test-secret';
    process.env.ENABLE_DOC = 'true';
    process.env.ENABLE_WORDPERFECT = 'true';
    
    const config = new MCPConfig();
    
    // Verify values from environment variables
    expect(config.environment).toBe('production');
    expect(config.port).toBe(4000);
    expect(config.logLevel).toBe('warn');
    expect(config.cache.enabled).toBe(true);
    expect(config.cache.type).toBe('redis');
    expect(config.cache.redis?.url).toBe('redis://localhost:6379');
    expect(config.auth.apiKeys).toEqual(['key1', 'key2', 'key3']);
    expect(config.auth.jwtSecret).toBe('test-secret');
    expect(config.legacyFormats.enableDOC).toBe(true);
    expect(config.legacyFormats.enableWordPerfect).toBe(true);
  });
  
  test('should handle boolean environment variables correctly', () => {
    // Set environment variables with different boolean formats
    process.env.CACHE_ENABLED = 'false';
    process.env.ENABLE_DOC = '0';
    process.env.ENABLE_WORDPERFECT = 'TRUE';
    
    const config = new MCPConfig();
    
    // Verify boolean conversion
    expect(config.cache.enabled).toBe(false);
    expect(config.legacyFormats.enableDOC).toBe(false);
    expect(config.legacyFormats.enableWordPerfect).toBe(true);
  });
  
  test('should handle numeric environment variables correctly', () => {
    // Set environment variables with numeric values
    process.env.MCP_PORT = '5000';
    process.env.RATE_LIMIT = '200';
    process.env.CACHE_TTL = '7200';
    process.env.CACHE_MAX_SIZE = '500';
    
    const config = new MCPConfig();
    
    // Verify numeric conversion
    expect(config.port).toBe(5000);
    expect(config.rateLimit).toBe(200);
    expect(config.cache.ttl).toBe(7200);
    expect(config.cache.maxSize).toBe(500);
  });
  
  test('should handle invalid environment variables gracefully', () => {
    // Set invalid environment variables
    process.env.MCP_PORT = 'not-a-number';
    process.env.CACHE_ENABLED = 'not-a-boolean';
    
    const config = new MCPConfig();
    
    // Verify fallback to defaults
    expect(config.port).toBe(3030); // Default value
    expect(config.cache.enabled).toBe(true); // Default value
  });
});