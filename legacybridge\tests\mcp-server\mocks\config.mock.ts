// Mock MCPConfig for testing
export const mockMCPConfig = {
  environment: 'test',
  version: '1.0.0-test',
  port: 3031,
  logLevel: 'error',
  corsOrigins: '*',
  rateLimit: 100,
  enableDashboard: false,
  cache: {
    enabled: false,
    type: 'memory',
    ttl: 3600,
    maxSize: 100
  },
  auth: {
    apiKeys: ['test-api-key-1', 'test-api-key-2'],
    jwtSecret: 'test-jwt-secret',
    jwtExpiration: '1h'
  },
  legacyFormats: {
    enableDOC: false,
    enableWordPerfect: false
  }
};